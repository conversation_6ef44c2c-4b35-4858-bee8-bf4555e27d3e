同花顺选股公式使用说明
===================

一、公式功能说明
--------------
本选股公式用于筛选符合以下技术形态的股票：
1. 前期涨停或冲高放量
2. 回调整理
3. 再次放量突破
4. 形成二次启动的技术形态

二、选股逻辑详解
--------------
1. 第一阶段：寻找30天内的涨停或冲高日
   - 涨停：涨幅≥9.5%
   - 冲高：最高价涨幅≥7%且收盘涨幅≥3%
   - 当日成交量≥日常成交量的2倍

2. 第二阶段：确认回调整理
   - 股价回调至第一次涨停/冲高日最低点以下
   - 形成横盘整理形态

3. 第三阶段：寻找再次放量
   - 近5日内出现成交量≥日常成交量2倍的交易日
   - 该日股价表现为涨停或冲高回落收阳线

4. 第四阶段：确认技术突破
   - 第二次放量日的最低价高于第一次涨停/冲高日的最高价
   - 后续回调未跌破第二次放量日的最低点

三、参数设置
-----------
- N = 30：回看天数，可根据需要调整为20-45天
- M = 5：近期天数，可调整为3-7天
- VOL_RATIO = 2：成交量放大倍数，可调整为1.5-3倍

四、使用方法
-----------
1. 打开同花顺软件
2. 进入"功能" → "选股器" → "条件选股"
3. 点击"条件设定" → "其他类型" → "技术指标"
4. 选择"导入公式"或"新建公式"
5. 将公式代码复制粘贴到编辑器中
6. 保存公式并命名（如：涨停回调再放量）
7. 执行选股，查看结果

五、注意事项
-----------
1. 本公式适用于A股市场，涨停板制度下的股票
2. 建议结合基本面分析，避免选中问题股票
3. 选股结果仅供参考，投资需谨慎
4. 可根据市场环境调整参数设置
5. 建议在不同市场阶段测试公式有效性

六、优化建议
-----------
1. 可增加换手率条件，过滤流动性差的股票
2. 可增加均线条件，确保股票处于上升趋势
3. 可增加市值条件，过滤超小盘股票
4. 可增加行业过滤，避开高风险行业

七、风险提示
-----------
1. 技术分析存在滞后性，不能保证未来走势
2. 市场环境变化可能影响公式有效性
3. 需要结合资金管理和风险控制
4. 建议分批建仓，控制单只股票仓位

八、公式测试
-----------
建议在使用前进行回测：
1. 选择历史时间段进行测试
2. 统计选股成功率和收益率
3. 分析失败案例，优化公式参数
4. 在不同市场环境下测试稳定性
