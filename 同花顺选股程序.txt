{同花顺选股公式 - 涨停回调再启动模式}

{选股条件说明：
1. 30天内出现过涨停或冲高价
2. 涨停当日成交量较日常增加2倍
3. 后期股价回调至涨停日最低点以下横盘多日
4. 近5日内再次出现成交量达到日常成交量的2倍
5. 股价再次涨停或当日冲高回落收阳线
6. 当日最低价高于之前涨停/放量日的最高点
7. 之后回调但未跌破第二次放量的最低点}

{选股公式代码}
{定义变量}
涨停价:=C*1.098;
平均成交量20:=MA(V,20);
平均成交量10:=MA(V,10);

{第一阶段：30天内涨停或冲高}
涨停条件:=C>=REF(C,1)*1.095 AND C=H;
冲高条件:=(H-REF(C,1))/REF(C,1)>=0.07 AND C>REF(C,1);
第一次放量:=V>=REF(平均成交量20,1)*2;
第一阶段满足:=COUNT(涨停条件 OR 冲高条件,30)>=1 AND COUNT(第一次放量,30)>=1;

{找到第一次涨停或冲高的位置}
第一次信号日:=BARSLAST(涨停条件 OR 冲高条件);
第一次信号最高价:=REF(H,第一次信号日);
第一次信号最低价:=REF(L,第一次信号日);
第一次信号收盘价:=REF(C,第一次信号日);

{第二阶段：回调至涨停日最低点以下横盘}
回调条件:=L<第一次信号最低价;
横盘天数:=BARSLAST(NOT 回调条件);
横盘满足:=横盘天数>=3 AND 横盘天数<=15;

{第三阶段：近5日内再次放量}
近期放量:=COUNT(V>=平均成交量20*2,5)>=1;
第二次放量日:=BARSLAST(V>=平均成交量20*2);

{第四阶段：再次涨停或冲高回落收阳线}
再次涨停:=C>=REF(C,1)*1.095 AND C=H;
冲高回落阳线:=(H-REF(C,1))/REF(C,1)>=0.05 AND C>O AND C>REF(C,1);
第二次信号:=再次涨停 OR 冲高回落阳线;

{第五阶段：当日最低价高于第一次信号最高价}
最低价突破:=L>第一次信号最高价;

{第六阶段：回调不破第二次放量最低点}
第二次放量最低价:=REF(L,第二次放量日);
未破支撑:=L>=第二次放量最低价*0.98;

{综合条件}
条件1:=第一阶段满足 AND 第一次信号日<=30 AND 第一次信号日>=5;
条件2:=横盘满足;
条件3:=近期放量 AND 第二次放量日<=5;
条件4:=第二次信号;
条件5:=最低价突破;
条件6:=未破支撑;

{最终选股条件}
选股信号:=条件1 AND 条件2 AND 条件3 AND 条件4 AND 条件5 AND 条件6;

{输出结果}
选股信号;

{辅助指标 - 可选显示}
{XG1:第一次信号日<=30,COLORRED;}
{XG2:横盘满足,COLORYELLOW;}
{XG3:近期放量,COLORBLUE;}
{XG4:第二次信号,COLORMAGENTA;}
{XG5:最低价突破,COLORGREEN;}
{XG6:未破支撑,COLORCYAN;}

{使用说明：
1. 将以上代码复制到同花顺选股器的条件选股中
2. 选择"技术指标选股"
3. 粘贴代码并保存为自定义指标
4. 运行选股即可筛选符合条件的股票
5. 建议结合基本面分析和市场环境判断}

{注意事项：
1. 此公式基于技术分析，需结合基本面
2. 建议在牛市或震荡市中使用
3. 熊市中谨慎使用此策略
4. 建议设置止损位，控制风险
5. 可根据实际情况调整参数}
