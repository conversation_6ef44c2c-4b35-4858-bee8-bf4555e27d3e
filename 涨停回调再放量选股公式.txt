{同花顺选股程序 - 涨停回调再次放量选股}

{选股条件说明：
1. 30天内出现过涨停或冲高价
2. 涨停当日成交量较日常增加2倍
3. 后期股价回调至涨停日最低点以下横盘多日
4. 近5日内再次出现成交量达到日常成交量的2倍
5. 股价再次出现涨停或当日冲高回落收阳线
6. 当日股价最低点高于之前涨停或放量日的最高点
7. 之后出现股价回调但未跌破第二次放量的最低点
}

{选股公式代码}

{第一步：定义基础变量}
N:=30; {回看天数}
M:=5;  {近期天数}
VOL_RATIO:=2; {成交量倍数}

{计算日常平均成交量（20日均量）}
AVG_VOL:=MA(V,20);

{第二步：寻找30天内的涨停或冲高日}
{涨停条件：涨幅>=9.5%}
LIMIT_UP:=C/REF(C,1)>=1.095;

{冲高条件：当日最高价较前日收盘涨幅>=7%且收盘涨幅>=3%}
HIGH_RUSH:=H/REF(C,1)>=1.07 AND C/REF(C,1)>=1.03;

{涨停或冲高条件}
TARGET_DAY:=LIMIT_UP OR HIGH_RUSH;

{第三步：检查涨停/冲高当日成交量是否放大2倍}
VOL_AMPLIFY:=V>=VOL_RATIO*REF(AVG_VOL,1);

{第四步：寻找符合条件的第一次涨停/冲高日}
FIRST_TARGET:=TARGET_DAY AND VOL_AMPLIFY;

{获取最近30天内最后一次符合条件的涨停/冲高日}
FIRST_DAY_EXIST:=COUNT(FIRST_TARGET,N)>0;
FIRST_DAY_BARSLAST:=BARSLAST(FIRST_TARGET);

{第五步：检查股价是否回调至涨停日最低点以下}
{获取第一次涨停/冲高日的最低价}
FIRST_LOW:=REF(L,FIRST_DAY_BARSLAST);

{检查是否有回调至最低点以下的情况}
CALLBACK_BELOW:=COUNT(C<FIRST_LOW,FIRST_DAY_BARSLAST-1)>0;

{第六步：寻找近5日内的第二次放量}
{近5日成交量放大2倍}
RECENT_VOL_AMPLIFY:=V>=VOL_RATIO*AVG_VOL;
SECOND_VOL_DAY:=COUNT(RECENT_VOL_AMPLIFY,M)>0;

{第七步：检查第二次放量日的股价表现}
{获取最近一次放量日}
SECOND_DAY_BARSLAST:=BARSLAST(RECENT_VOL_AMPLIFY);

{第二次放量日是否涨停或冲高回落收阳}
SECOND_LIMIT_UP:=REF(LIMIT_UP,SECOND_DAY_BARSLAST);
SECOND_HIGH_RUSH:=REF(HIGH_RUSH,SECOND_DAY_BARSLAST);
SECOND_YANG:=REF(C>O,SECOND_DAY_BARSLAST); {收阳线}

SECOND_TARGET_VALID:=SECOND_LIMIT_UP OR (SECOND_HIGH_RUSH AND SECOND_YANG);

{第八步：检查第二次放量日最低价是否高于第一次涨停/冲高日最高价}
FIRST_HIGH:=REF(H,FIRST_DAY_BARSLAST);
SECOND_LOW:=REF(L,SECOND_DAY_BARSLAST);
LOW_ABOVE_HIGH:=SECOND_LOW>FIRST_HIGH;

{第九步：检查后续回调是否未跌破第二次放量日最低点}
{从第二次放量日之后到今天，最低价是否都高于第二次放量日最低点}
NO_BREAK_SECOND_LOW:=IF(SECOND_DAY_BARSLAST>0,LLV(L,SECOND_DAY_BARSLAST)>SECOND_LOW,1);

{第十步：综合条件判断}
CONDITION1:=FIRST_DAY_EXIST; {30天内有涨停或冲高}
CONDITION2:=FIRST_DAY_BARSLAST<=N AND FIRST_DAY_BARSLAST>=M; {确保时间间隔合理}
CONDITION3:=CALLBACK_BELOW; {有回调至涨停日最低点以下}
CONDITION4:=SECOND_VOL_DAY; {近5日有放量}
CONDITION5:=SECOND_TARGET_VALID; {第二次放量日股价表现符合要求}
CONDITION6:=LOW_ABOVE_HIGH; {第二次放量日最低价高于第一次最高价}
CONDITION7:=NO_BREAK_SECOND_LOW; {后续未跌破第二次放量日最低点}

{最终选股条件}
CONDITION1 AND CONDITION2 AND CONDITION3 AND CONDITION4 AND CONDITION5 AND CONDITION6 AND CONDITION7;

{辅助信息输出（可选）}
{第一次涨停/冲高日距今天数}
FIRST_DAYS_AGO:=FIRST_DAY_BARSLAST;

{第二次放量日距今天数}
SECOND_DAYS_AGO:=SECOND_DAY_BARSLAST;

{第一次涨停/冲高日涨幅}
FIRST_GAIN:=(REF(C,FIRST_DAY_BARSLAST)/REF(C,FIRST_DAY_BARSLAST+1)-1)*100;

{第二次放量日涨幅}
SECOND_GAIN:=(REF(C,SECOND_DAY_BARSLAST)/REF(C,SECOND_DAY_BARSLAST+1)-1)*100;
