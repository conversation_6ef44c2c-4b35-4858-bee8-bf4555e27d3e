/* 同花顺选股公式 - 涨停回调再放量（稳定版） */

/* 参数定义 */
N:=30;
M:=5;

/* 基础数据 */
AVG_VOL:=MA(V,20);
VOL_RATIO:=V/AVG_VOL;

/* 涨停和冲高定义 */
ZHANGTING:=C/REF(C,1)>=1.095;
CHONGGAO:=H/REF(C,1)>=1.07 AND C/REF(C,1)>=1.03;
MUBIAO:=ZHANGTING OR CHONGGAO;

/* 放量条件 */
FANGLIANG:=VOL_RATIO>=2;

/* 第一次涨停或冲高且放量 */
FIRST_DAY:=MUBIAO AND FANGLIANG;

/* 30天内是否有第一次目标日 */
A1:=COUNT(FIRST_DAY,N)>0;

/* 获取最近一次第一目标日的位置 */
FIRST_POS:=BARSLAST(FIRST_DAY);

/* 第一次目标日的最低价和最高价 */
FIRST_L:=REF(L,FIRST_POS);
FIRST_H:=REF(H,FIRST_POS);

/* 是否有回调到第一次目标日最低价以下 */
A2:=COUNT(C<FIRST_L,FIRST_POS-1)>0;

/* 近5日是否有放量 */
RECENT_VOL:=VOL_RATIO>=2;
A3:=COUNT(RECENT_VOL,M)>0;

/* 最近一次放量日位置 */
SECOND_POS:=BARSLAST(RECENT_VOL);

/* 第二次放量日是否涨停或冲高收阳 */
SECOND_ZT:=REF(ZHANGTING,SECOND_POS);
SECOND_CG:=REF(CHONGGAO,SECOND_POS);
SECOND_YANG:=REF(C>O,SECOND_POS);
A4:=SECOND_ZT OR (SECOND_CG AND SECOND_YANG);

/* 第二次放量日最低价是否高于第一次最高价 */
SECOND_L:=REF(L,SECOND_POS);
A5:=SECOND_L>FIRST_H;

/* 后续是否未跌破第二次放量日最低价 */
A6:=LLV(L,SECOND_POS)>SECOND_L;

/* 时间条件检查 */
A7:=FIRST_POS<=N AND FIRST_POS>=M;
A8:=SECOND_POS<M;

/* 综合条件 */
A1 AND A2 AND A3 AND A4 AND A5 AND A6 AND A7 AND A8;
