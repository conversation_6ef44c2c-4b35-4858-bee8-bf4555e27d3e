/* 同花顺选股公式 - 涨停回调再放量（简化版） */

/* 基础参数设置 */
N:=30;
M:=5;
VOL_RATIO:=2;

/* 计算平均成交量 */
AVG_VOL:=MA(V,20);

/* 第一步：识别涨停或冲高日 */
LIMIT_UP:=C/REF(C,1)>=1.095;
HIGH_RUSH:=H/REF(C,1)>=1.07 AND C/REF(C,1)>=1.03;
TARGET_DAY:=LIMIT_UP OR HIGH_RUSH;

/* 第二步：检查成交量放大 */
VOL_AMPLIFY:=V>=VOL_RATIO*REF(AVG_VOL,1);

/* 第三步：寻找第一次目标日 */
FIRST_TARGET:=TARGET_DAY AND VOL_AMPLIFY;
FIRST_EXIST:=COUNT(FIRST_TARGET,N)>0;
FIRST_BARS:=BARSLAST(FIRST_TARGET);

/* 第四步：检查回调条件 */
FIRST_LOW:=REF(L,FIRST_BARS);
HAS_CALLBACK:=COUNT(C<FIRST_LOW,FIRST_BARS-1)>0;

/* 第五步：寻找第二次放量 */
RECENT_VOL:=V>=VOL_RATIO*AVG_VOL;
SECOND_EXIST:=COUNT(RECENT_VOL,M)>0;
SECOND_BARS:=BARSLAST(RECENT_VOL);

/* 第六步：检查第二次放量日表现 */
SECOND_UP:=REF(LIMIT_UP,SECOND_BARS);
SECOND_RUSH:=REF(HIGH_RUSH,SECOND_BARS);
SECOND_YANG:=REF(C>O,SECOND_BARS);
SECOND_VALID:=SECOND_UP OR (SECOND_RUSH AND SECOND_YANG);

/* 第七步：检查价格关系 */
FIRST_HIGH:=REF(H,FIRST_BARS);
SECOND_LOW:=REF(L,SECOND_BARS);
PRICE_VALID:=SECOND_LOW>FIRST_HIGH;

/* 第八步：检查未跌破条件 */
NO_BREAK:=IF(SECOND_BARS>0,LLV(L,SECOND_BARS)>SECOND_LOW,1);

/* 第九步：时间条件 */
TIME_VALID:=FIRST_BARS<=N AND FIRST_BARS>=M AND SECOND_BARS<M;

/* 最终选股条件 */
FIRST_EXIST AND HAS_CALLBACK AND SECOND_EXIST AND SECOND_VALID AND PRICE_VALID AND NO_BREAK AND TIME_VALID;
